node_modules
.DS_Store
dist
dist-ssr
*.local
.env
.env.*
!.env.example
modellibrary
macroLibrary

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Rust/Tauri specific
src-tauri/target/
**/*.rs.bk
Cargo.lock

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
src-tauri/gen/schemas/desktop-schema.json
src-tauri/gen/schemas/windows-schema.json
src-tauri/gen/schemas/desktop-schema.json
src-tauri/gen/schemas/windows-schema.json
