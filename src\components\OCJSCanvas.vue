<template>
  <div class="ocjs-canvas-container">
    <!-- Loading indicator -->
    <div v-if="isProcessing" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <p class="loading-text">{{ $t('ocjs.processing') }}</p>
        <p class="loading-detail">{{ processingStep }}</p>
      </div>
    </div>

    <!-- Error display -->
    <div v-if="error" class="error-overlay">
      <div class="error-content">
        <h3 class="error-title">{{ $t('ocjs.error') }}</h3>
        <p class="error-message">{{ error }}</p>
        <button @click="retryProcessing" class="retry-button">
          {{ $t('ocjs.retry') }}
        </button>
      </div>
    </div>

    <!-- 3D Model Viewer -->
    <div v-if="modelUrl && !isProcessing && !error" class="model-viewer-container">
      <div
        ref="threeContainer"
        class="three-viewer-wrapper"
        :style="{ width: '100%', height: '100%' }"
      ></div>
    </div>

    <!-- Empty state -->
    <div v-if="!modelUrl && !isProcessing && !error" class="empty-state">
      <div class="empty-content">
        <h3 class="empty-title">{{ $t('ocjs.noModel') }}</h3>
        <p class="empty-message">{{ $t('ocjs.noModelDescription') }}</p>
      </div>
    </div>

    <!-- Controls overlay - always visible for debugging -->
    <div class="controls-overlay">
      <button @click="downloadGLB" class="control-button" :title="$t('ocjs.download')" :disabled="!modelUrl">
        <Download :size="16" />
      </button>
      <button @click="resetCamera" class="control-button" :title="$t('ocjs.resetCamera')" :disabled="!scene">
        <RotateCcw :size="16" />
      </button>
      <button @click="toggleAutoRotate" class="control-button" :title="$t('ocjs.toggleRotation')" :disabled="!scene">
        <RotateCw :size="16" />
      </button>
      <button @click="toggleWireframe" class="control-button" title="Toggle Wireframe" :disabled="!scene">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
          <line x1="9" y1="9" x2="15" y2="15"/>
          <line x1="15" y1="9" x2="9" y2="15"/>
        </svg>
      </button>
      <button @click="debugModel" class="control-button" title="Debug Model Info">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="10"/>
          <line x1="12" y1="16" x2="12" y2="12"/>
          <line x1="12" y1="8" x2="12.01" y2="8"/>
        </svg>
      </button>
      <button @click="testWorker" class="control-button" title="Test Worker" :disabled="isProcessing">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M9 12l2 2 4-4"/>
          <circle cx="12" cy="12" r="10"/>
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
// WORKER-BASED OCJS PROCESSING - v2.1 - FORCE SERVICE RELOAD: 2025-01-04-12:05
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { Download, RotateCcw, RotateCw } from 'lucide-vue-next'
import { cncToolService } from '../services/cncToolService'
import { ocjsService } from '../services/ocjsService'
import type { DrawCommand } from '../types'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'

interface Props {
  drawCommands: DrawCommand[]
}

const props = withDefaults(defineProps<Props>(), {
  drawCommands: () => []
})

// Reactive state
const isProcessing = ref(false)
const processingStep = ref('')
const error = ref<string | null>(null)
const modelUrl = ref<string | null>(null)
const glbData = ref<ArrayBuffer | null>(null)

// Three.js references
let scene: THREE.Scene | null = null
let camera: THREE.PerspectiveCamera | null = null
let renderer: THREE.WebGLRenderer | null = null
let controls: OrbitControls | null = null
let model: THREE.Group | null = null
let animationId: number | null = null
let autoRotate = false
let wireframeMode = false
const threeContainer = ref<HTMLDivElement>()



// Process draw commands using worker service
const processDrawCommands = async () => {
  console.log('🚀 WORKER-BASED PROCESSING STARTED')

  if (!props.drawCommands || props.drawCommands.length === 0) {
    modelUrl.value = null
    return
  }

  isProcessing.value = true
  error.value = null

  try {
    processingStep.value = 'Parsing layers...'

    // Parse commands for OC.js processing
    const parsed = cncToolService.parseCommandsForOCJS(props.drawCommands)
    console.log('🔍 Parsed commands:', parsed)

    if (parsed.panelCommands.length === 0) {
      throw new Error('No PANEL layer found - cannot create door body')
    }

    processingStep.value = 'Extracting door parameters...'

    // Extract door parameters using the service
    console.log('🔧 Using OCJS Service to extract door parameters')
    const doorParams = ocjsService.extractDoorParameters(parsed.panelCommands)
    console.log('📏 Extracted door parameters:', doorParams)

    // Convert to meters for better Three.js visualization
    const doorParamsMeters = {
      width: doorParams.width / 1000,
      height: doorParams.height / 1000,
      thickness: doorParams.thickness / 1000,
      cornerRadius: doorParams.cornerRadius ? doorParams.cornerRadius / 1000 : undefined
    }

    console.log('Door parameters:', doorParamsMeters)
    console.log('Door size in mm: W=' + doorParams.width + ' H=' + doorParams.height + ' T=' + doorParams.thickness)

    processingStep.value = 'Processing with worker...'

    // Prepare tool data for worker processing
    const topTools = parsed.topTools.map(tool => ({
      tool: tool.tool,
      commands: tool.commands,
      depth: tool.depth / 1000 // Convert to meters
    }))

    const bottomTools = parsed.bottomTools.map(tool => ({
      tool: tool.tool,
      commands: tool.commands,
      depth: tool.depth / 1000 // Convert to meters
    }))

    console.log('⚙️ Sending to worker:', { doorParamsMeters, topTools, bottomTools })

    // Process everything in the worker
    const glbData = await ocjsService.processDoorWithTools(
      doorParamsMeters,
      topTools,
      bottomTools
    )

    console.log('✅ Worker processing completed, GLB data size:', glbData.byteLength)

    processingStep.value = 'Creating 3D model...'

    // Create blob URL from GLB data
    const glbBlob = new Blob([glbData], { type: 'model/gltf-binary' })
    const glbUrl = URL.createObjectURL(glbBlob)

    // Clean up previous URL
    if (modelUrl.value) {
      URL.revokeObjectURL(modelUrl.value)
    }

    modelUrl.value = glbUrl
    console.log('Model URL created:', modelUrl.value)

    // Wait a bit then create Three.js viewer
    setTimeout(() => {
      if (threeContainer.value) {
        const success = initThreeJS()
        if (success) {
          loadGLBModel()
        } else {
          console.error('❌ Failed to initialize Three.js')
        }
      } else {
        console.error('❌ Three.js container not available after worker processing')
      }
    }, 100)

  } catch (err) {
    console.error('OCJS Worker processing error:', err)
    error.value = err instanceof Error ? err.message : 'Unknown error occurred'
  } finally {
    isProcessing.value = false
    processingStep.value = ''
  }
}

// Initialize Three.js scene
const initThreeJS = () => {
  if (!threeContainer.value) {
    console.error('❌ Cannot create Three.js viewer - missing container')
    return false
  }

  // Check if container is actually in the DOM and visible
  if (!threeContainer.value.offsetParent && threeContainer.value.offsetWidth === 0) {
    console.error('❌ Three.js container is not visible or not in DOM')
    return false
  }

  console.log('✅ Initializing Three.js scene')
  console.log('📦 Container element:', threeContainer.value)
  console.log('📐 Container dimensions:', {
    width: threeContainer.value.clientWidth,
    height: threeContainer.value.clientHeight,
    offsetWidth: threeContainer.value.offsetWidth,
    offsetHeight: threeContainer.value.offsetHeight
  })

  // Clear existing content
  threeContainer.value.innerHTML = ''

  // Scene
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0xe0e0e0)

  // Camera
  const width = threeContainer.value.clientWidth || 800
  const height = threeContainer.value.clientHeight || 600
  const aspect = width / height

  console.log('Container dimensions:', { width, height, aspect })

  camera = new THREE.PerspectiveCamera(45, aspect, 0.1, 1000)
  camera.position.set(2, 1.5, 2)
  camera.lookAt(0, 0, 0)

  // Renderer
  renderer = new THREE.WebGLRenderer({ antialias: true })
  renderer.setSize(width, height)
  renderer.setPixelRatio(window.devicePixelRatio)
  renderer.shadowMap.enabled = true
  renderer.shadowMap.type = THREE.PCFSoftShadowMap
  renderer.outputColorSpace = THREE.SRGBColorSpace
  threeContainer.value.appendChild(renderer.domElement)

  console.log('Renderer created with size:', width, 'x', height)
  console.log('Canvas element:', renderer.domElement)
  console.log('Canvas style:', renderer.domElement.style.cssText)

  // Controls
  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableDamping = true
  controls.dampingFactor = 0.05
  controls.screenSpacePanning = false
  controls.minDistance = 0.5
  controls.maxDistance = 10
  controls.maxPolarAngle = Math.PI

  // Lighting
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
  scene.add(ambientLight)

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
  directionalLight.position.set(5, 5, 5)
  directionalLight.castShadow = true
  directionalLight.shadow.mapSize.width = 2048
  directionalLight.shadow.mapSize.height = 2048
  directionalLight.shadow.camera.near = 0.5
  directionalLight.shadow.camera.far = 50
  scene.add(directionalLight)

  // Add a test cube to verify Three.js is working
  const testGeometry = new THREE.BoxGeometry(0.2, 0.2, 0.2)
  const testMaterial = new THREE.MeshBasicMaterial({
    color: 0xff0000,
    wireframe: false,
    transparent: false
  })
  const testCube = new THREE.Mesh(testGeometry, testMaterial)
  testCube.position.set(0, 0, 0)
  scene.add(testCube)
  console.log('Test cube added to scene at position:', testCube.position)

  // Start animation loop
  animate()

  console.log('✅ Three.js scene initialized successfully')
  return true
}

// Load GLB model
const loadGLBModel = () => {
  if (!scene || !modelUrl.value) {
    console.error('❌ Cannot load model - missing scene or URL')
    console.log('🔍 Debug info:', { scene: !!scene, modelUrl: modelUrl.value })
    return
  }

  console.log('🔄 Loading GLB model:', modelUrl.value)

  const loader = new GLTFLoader()
  loader.load(
    modelUrl.value,
    (gltf) => {
      console.log('GLB model loaded successfully')

      // Remove existing model if any
      if (model) {
        scene!.remove(model)
      }

      model = gltf.scene
      scene!.add(model)

      // Enable shadows for all meshes
      model.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          child.castShadow = true
          child.receiveShadow = true
        }
      })

      // Calculate bounding box and center the model
      const box = new THREE.Box3().setFromObject(model)
      const center = box.getCenter(new THREE.Vector3())
      const size = box.getSize(new THREE.Vector3())

      console.log('Model bounding box:', {
        center: center,
        size: size,
        min: box.min,
        max: box.max
      })

      // Center the model
      model.position.sub(center)

      // Adjust camera position based on model size
      const maxDim = Math.max(size.x, size.y, size.z)
      console.log('Max dimension:', maxDim)

      // Ensure minimum distance for very small models
      const distance = Math.max(maxDim * 3, 1.0) // Minimum 1 unit distance
      console.log('Camera distance:', distance)

      camera!.position.set(distance, distance * 0.75, distance)
      camera!.lookAt(0, 0, 0)

      if (controls) {
        controls.target.set(0, 0, 0)
        controls.update()
      }

      // Add a helper to visualize the model bounds
      const boxHelper = new THREE.Box3Helper(box, 0xff0000)
      scene!.add(boxHelper)

      console.log('Model centered and camera adjusted')
      console.log('Camera position:', camera!.position)
      console.log('Model position:', model.position)
    },
    (progress) => {
      console.log('Loading progress:', (progress.loaded / progress.total * 100) + '%')
    },
    (error) => {
      console.error('Error loading GLB model:', error)
    }
  )
}

// Animation loop
let frameCount = 0
const animate = () => {
  animationId = requestAnimationFrame(animate)

  if (controls) {
    controls.update()
  }

  // Auto rotate
  if (autoRotate && model) {
    model.rotation.y += 0.005
  }

  if (renderer && scene && camera) {
    renderer.render(scene, camera)

    // Debug every 60 frames (roughly once per second)
    frameCount++
    if (frameCount % 60 === 0) {
      console.log('Rendering frame', frameCount, 'Scene children:', scene.children.length)
    }
  }
}

// Control functions
const resetCamera = () => {
  if (!camera || !controls || !model) return

  // Calculate bounding box and reset camera
  const box = new THREE.Box3().setFromObject(model)
  const size = box.getSize(new THREE.Vector3())
  const maxDim = Math.max(size.x, size.y, size.z)
  const distance = maxDim * 2

  camera.position.set(distance, distance * 0.75, distance)
  camera.lookAt(0, 0, 0)
  controls.target.set(0, 0, 0)
  controls.update()

  console.log('Camera reset to default position')
}

const toggleAutoRotate = () => {
  autoRotate = !autoRotate
  console.log('Auto rotate:', autoRotate)
}

const toggleWireframe = () => {
  if (!model) return

  wireframeMode = !wireframeMode
  model.traverse((child) => {
    if (child instanceof THREE.Mesh && child.material) {
      if (Array.isArray(child.material)) {
        child.material.forEach(mat => {
          if (mat instanceof THREE.MeshStandardMaterial) {
            mat.wireframe = wireframeMode
          }
        })
      } else if (child.material instanceof THREE.MeshStandardMaterial) {
        child.material.wireframe = wireframeMode
      }
    }
  })
  console.log('Wireframe mode:', wireframeMode)
}

const debugModel = () => {
  console.log('=== THREE.JS DEBUG INFO ===')
  console.log('Scene:', scene)
  console.log('Camera:', camera)
  console.log('Renderer:', renderer)
  console.log('Model:', model)
  console.log('Container:', threeContainer.value)

  if (threeContainer.value) {
    console.log('Container dimensions:', {
      width: threeContainer.value.clientWidth,
      height: threeContainer.value.clientHeight
    })
  }

  if (renderer) {
    console.log('Renderer size:', renderer.getSize(new THREE.Vector2()))
  }

  if (scene) {
    console.log('Scene children:', scene.children.length)
    scene.children.forEach((child, index) => {
      console.log(`Child ${index}:`, child.type, child)
    })
  }

  if (model && scene && camera) {
    const box = new THREE.Box3().setFromObject(model)
    const size = box.getSize(new THREE.Vector3())
    const center = box.getCenter(new THREE.Vector3())

    console.log('Model info:')
    console.log('- Size:', size)
    console.log('- Center:', center)
    console.log('- Position:', model.position)
    console.log('- Camera position:', camera.position)
    console.log('- Camera looking at:', controls?.target || 'unknown')

    // Check if model has any meshes
    let meshCount = 0
    model.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        meshCount++
        console.log('Mesh found:', child.geometry, child.material)
      }
    })
    console.log('Total meshes in model:', meshCount)
  }
  console.log('=== END DEBUG INFO ===')
}

// Handle window resize
const handleResize = () => {
  if (!camera || !renderer || !threeContainer.value) return

  const width = threeContainer.value.clientWidth
  const height = threeContainer.value.clientHeight

  camera.aspect = width / height
  camera.updateProjectionMatrix()
  renderer.setSize(width, height)
}

// Retry processing
const retryProcessing = () => {
  error.value = null
  processDrawCommands()
}

// Download GLB file
const downloadGLB = () => {
  if (!modelUrl.value) return

  const link = document.createElement('a')
  link.href = modelUrl.value
  link.download = 'door_model.glb'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}



// Watch for draw commands changes
watch(() => props.drawCommands, () => {
  processDrawCommands()
}, { immediate: true, deep: true })

// Test worker connectivity
const testWorker = async () => {
  console.log('🧪 Testing OCJS Worker connectivity...')
  try {
    processingStep.value = 'Testing worker...'
    console.log('📞 Calling ocjsService.testWorker()')

    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Worker test timeout after 10 seconds')), 10000)
    })

    const testPromise = ocjsService.testWorker()
    const isWorking = await Promise.race([testPromise, timeoutPromise]) as boolean

    if (isWorking) {
      console.log('✅ OCJS Worker is working correctly')
      alert('✅ OCJS Worker test passed!')
    } else {
      console.error('❌ OCJS Worker test failed')
      alert('❌ OCJS Worker test failed!')
    }
    processingStep.value = ''
    return isWorking
  } catch (error) {
    console.error('❌ OCJS Worker test error:', error)
    alert(`❌ OCJS Worker test error: ${error.message}`)
    processingStep.value = ''
    return false
  }
}

// Component lifecycle
onMounted(() => {
  console.log('OCJSCanvas mounted')
  window.addEventListener('resize', handleResize)

  // Test worker connectivity
  setTimeout(() => {
    testWorker()
  }, 1000)

  // Initialize Three.js immediately if we have a container
  setTimeout(() => {
    if (threeContainer.value && !scene) {
      console.log('🔧 Force initializing Three.js on mount')
      const success = initThreeJS()
      if (success) {
        console.log('✅ Three.js initialized on mount')
      } else {
        console.log('❌ Failed to initialize Three.js on mount')
      }
    }
  }, 500)
})

onUnmounted(() => {
  console.log('OCJSCanvas unmounted')

  // Clean up Three.js
  if (animationId) {
    cancelAnimationFrame(animationId)
  }

  if (controls) {
    controls.dispose()
  }

  if (renderer) {
    renderer.dispose()
  }

  window.removeEventListener('resize', handleResize)

  // Clean up blob URL
  if (modelUrl.value) {
    URL.revokeObjectURL(modelUrl.value)
  }

  // Note: We don't dispose the worker service here as it's a singleton
  // and may be used by other components
})
</script>

<style scoped>
.ocjs-canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-content {
  text-align: center;
  padding: 2rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.loading-detail {
  font-size: 0.9rem;
  color: #6b7280;
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.error-content {
  text-align: center;
  padding: 2rem;
  max-width: 400px;
}

.error-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 1rem;
}

.error-message {
  color: #374151;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.retry-button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background: #2563eb;
}

.model-viewer-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.controls-overlay {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  z-index: 5;
}

.control-button {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-button:hover:not(:disabled) {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.control-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
}

.empty-content {
  text-align: center;
  max-width: 300px;
}

.empty-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.empty-message {
  color: #6b7280;
  line-height: 1.5;
}

/* Three.js viewer styling */
.three-viewer-wrapper {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
}

.three-viewer-wrapper canvas {
  display: block;
  width: 100%;
  height: 100%;
}
</style>
