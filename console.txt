 ✅ Registered theme: Vibrant Dark (vibrant-dark)
 ✅ Registered theme: Vibrant Light (vibrant-light)
 ✅ Registered theme: Neon Dark (neon-dark)
 OC.js Worker initialized
 Colorful themes initialized: Array(3)
 Adeko Lua Editörü b<PERSON>ıldı
 Created welcome file: Untitled
 Active file in group: group-1751617512320-5jvbvls3p File: Untitled Content length: 48
 Initializing Monaco Editor with content: -- <PERSON><PERSON>
-- Kodu<PERSON><PERSON> buraya ekleyin

...
 Monaco Editor created successfully
 Editor value: -- <PERSON><PERSON>
-- Kodu<PERSON>zu buraya ekleyin


 Container dimensions: Object
 Applied colorful theme: Vibrant Dark
 ✅ Applied colorful theme: vibrant-dark
 🔄 Forced re-tokenization
 Editor layout forced
 Line 1 tokens: Array(1)
 Line 2 tokens: Array(1)
 Line 3 tokens: Array(1)
 Line 4 tokens: Array(1)
 Creating new file: script.lua with content length: 2787
 Content preview: -- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThick...
 Active file in group: group-1751617512320-5jvbvls3p File: script.lua Content length: 2787
 Initializing Monaco Editor with content: -- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThick...
 Monaco Editor created successfully
 Editor value: -- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  a = 50    -- kenardan marjin
  g = 10    -- cam marjini
  c = 14    -- çita kalınlığı
  d = 6     -- cam derinliği
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()

  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  G.setLayer("H_Freze5mm_Ic")
  G.setThickness(-materialThickness+d-1)
  print(-materialThickness+d-1)
  local h = (Y-2*a)/3
  local t = {}
  t[1]  = {}    t[1][1],  t[1][2],  t[1][3],  t[1][4]  = {a, a}, {X-a, a}, {X/2, a+h/2}, {a, a}
  t[2]  = {}    t[2][1],  t[2][2],  t[2][3],  t[2][4]  = {a, a}, {X/2, a+h/2}, {a, a+h}, {a, a} 
  t[3]  = {}    t[3][1],  t[3][2],  t[3][3],  t[3][4]  = {X/2, a+h/2}, {X-a, a}, {X-a, a+h}, {X/2, a+h/2}
  t[4]  = {}    t[4][1],  t[4][2],  t[4][3],  t[4][4]  = {X/2, a+h/2}, {X-a, a+h}, {X/2, Y/2}, {a, a+h}, {X/2, a+h/2}
  t[5]  = {}    t[5][4],  t[5][3],  t[5][2],  t[5][1]  = {a, Y-a}, {X-a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a}
  t[6]  = {}    t[6][4],  t[6][3],  t[6][2],  t[6][1]  = {a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a-h}, {a, Y-a} 
  t[7]  = {}    t[7][4],  t[7][3],  t[7][2],  t[7][1]  = {X/2, Y-a-h/2}, {X-a, Y-a}, {X-a, Y-a-h}, {X/2, Y-a-h/2}
  t[8]  = {}    t[8][4],  t[8][3],  t[8][2],  t[8][1]  = {X/2, Y-a-h/2}, {X-a, Y-a-h}, {X/2, Y/2}, {a, Y-a-h}, {X/2, Y-a-h/2}
  t[9]  = {}    t[9][1],  t[9][2],  t[9][3],  t[9][4]  = {X/2, Y/2}, {X-a, a+h}, {X-a, a+2*h}, {X/2, Y/2}
  t[10] = {}    t[10][4], t[10][3], t[10][2], t[10][1] = {X/2, Y/2}, {a, a+h}, {a, a+2*h}, {X/2, Y/2}
  G.polylineimp( G.offSet(t[1],  -c/2) )
  G.polylineimp( G.offSet(t[2],  -c/2) )
  G.polylineimp( G.offSet(t[3],  -c/2) )
  G.polylineimp( G.offSet(t[4],  -c/2) )
  G.polylineimp( G.offSet(t[9],  -c/2) )
  G.polylineimp( G.offSet(t[5],  -c/2) )
  G.polylineimp( G.offSet(t[6],  -c/2) )
  G.polylineimp( G.offSet(t[7],  -c/2) )
  G.polylineimp( G.offSet(t[8],  -c/2) )
  G.polylineimp( G.offSet(t[10], -c/2) )
  
  G.setFace("bottom")
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-d)
  G.rectangle({a-g, a-g}, {X-a+g, Y-a+g})
  
  G.setLayer("K_Freze20mm_SF")
  for i = 0, 2, 1
  do
    G.line({a, a+i*h}, {X-a, a+(i+1)*h})
    G.line({X-a, a+i*h}, {a, a+(i+1)*h})
  end
  
  -- model parameters
  G.setFace("top")
  G.showPar({0, a+h/2}, {a+c/2, a+h/2}, "a+c/2")
  G.showPar({X/2, Y-a-c/2}, {X/2, Y}, "a+c/2")
  local o1 = G.offSet(t[1], c/2)
  local o2 = G.offSet(t[2], c/2)
  G.showPar(o1[3], o2[2], "c", 3)
  G.setFace("bottom")
  G.showPar({0, h/2}, {a-g, h/2}, "a-g")
  
  return true
end

require "ADekoDebugMode"
 Container dimensions: Object
 Applied colorful theme: Vibrant Dark
 ✅ Applied colorful theme: vibrant-dark
 🔄 Forced re-tokenization
 Editor layout forced
 Line 1 tokens: Array(1)
 Line 2 tokens: Array(1)
 Line 3 tokens: Array(1)
 Line 4 tokens: Array(1)
 Line 5 tokens: Array(1)
 === SENDING TO RUST ===
 Script content: -- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  a = 50    -- kenardan marjin
  g = 10    -- cam marjini
  c = 14    -- çita kalınlığı
  d = 6     -- cam derinliği
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()

  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  G.setLayer("H_Freze5mm_Ic")
  G.setThickness(-materialThickness+d-1)
  print(-materialThickness+d-1)
  local h = (Y-2*a)/3
  local t = {}
  t[1]  = {}    t[1][1],  t[1][2],  t[1][3],  t[1][4]  = {a, a}, {X-a, a}, {X/2, a+h/2}, {a, a}
  t[2]  = {}    t[2][1],  t[2][2],  t[2][3],  t[2][4]  = {a, a}, {X/2, a+h/2}, {a, a+h}, {a, a} 
  t[3]  = {}    t[3][1],  t[3][2],  t[3][3],  t[3][4]  = {X/2, a+h/2}, {X-a, a}, {X-a, a+h}, {X/2, a+h/2}
  t[4]  = {}    t[4][1],  t[4][2],  t[4][3],  t[4][4]  = {X/2, a+h/2}, {X-a, a+h}, {X/2, Y/2}, {a, a+h}, {X/2, a+h/2}
  t[5]  = {}    t[5][4],  t[5][3],  t[5][2],  t[5][1]  = {a, Y-a}, {X-a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a}
  t[6]  = {}    t[6][4],  t[6][3],  t[6][2],  t[6][1]  = {a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a-h}, {a, Y-a} 
  t[7]  = {}    t[7][4],  t[7][3],  t[7][2],  t[7][1]  = {X/2, Y-a-h/2}, {X-a, Y-a}, {X-a, Y-a-h}, {X/2, Y-a-h/2}
  t[8]  = {}    t[8][4],  t[8][3],  t[8][2],  t[8][1]  = {X/2, Y-a-h/2}, {X-a, Y-a-h}, {X/2, Y/2}, {a, Y-a-h}, {X/2, Y-a-h/2}
  t[9]  = {}    t[9][1],  t[9][2],  t[9][3],  t[9][4]  = {X/2, Y/2}, {X-a, a+h}, {X-a, a+2*h}, {X/2, Y/2}
  t[10] = {}    t[10][4], t[10][3], t[10][2], t[10][1] = {X/2, Y/2}, {a, a+h}, {a, a+2*h}, {X/2, Y/2}
  G.polylineimp( G.offSet(t[1],  -c/2) )
  G.polylineimp( G.offSet(t[2],  -c/2) )
  G.polylineimp( G.offSet(t[3],  -c/2) )
  G.polylineimp( G.offSet(t[4],  -c/2) )
  G.polylineimp( G.offSet(t[9],  -c/2) )
  G.polylineimp( G.offSet(t[5],  -c/2) )
  G.polylineimp( G.offSet(t[6],  -c/2) )
  G.polylineimp( G.offSet(t[7],  -c/2) )
  G.polylineimp( G.offSet(t[8],  -c/2) )
  G.polylineimp( G.offSet(t[10], -c/2) )
  
  G.setFace("bottom")
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-d)
  G.rectangle({a-g, a-g}, {X-a+g, Y-a+g})
  
  G.setLayer("K_Freze20mm_SF")
  for i = 0, 2, 1
  do
    G.line({a, a+i*h}, {X-a, a+(i+1)*h})
    G.line({X-a, a+i*h}, {a, a+(i+1)*h})
  end
  
  -- model parameters
  G.setFace("top")
  G.showPar({0, a+h/2}, {a+c/2, a+h/2}, "a+c/2")
  G.showPar({X/2, Y-a-c/2}, {X/2, Y}, "a+c/2")
  local o1 = G.offSet(t[1], c/2)
  local o2 = G.offSet(t[2], c/2)
  G.showPar(o1[3], o2[2], "c", 3)
  G.setFace("bottom")
  G.showPar({0, h/2}, {a-g, h/2}, "a-g")
  
  return true
end

require "ADekoDebugMode"
 Lua library path: ./LIBRARY\luaLibrary
 Debug mode: false
 === END SENDING TO RUST ===
 Full result object: {
  "success": true,
  "output": "DEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is table: 0000018418CC87C0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000018418CC87C0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000018418CC87C0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000018418CC87C0\nDEBUG: Engine is available, proceeding with engine calls\n-13\nDEBUG: ADekoLib.engine is table: 0000018418CC87C0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000018418CC87C0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000018418CC87C0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000018418CC87C0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000018418CC87C0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000018418CC87C0\nDEBUG: Engine is available, proceeding with engine calls\nDebug model JSON generated:\n{\n  \"models\": {\n    \"debug\": {\n      \"paths\": []\n    }\n  },\n  \"notes\": {\n    \"created\": \"2025-07-04 11:26:56\",\n    \"engine\": \"lua_makerjs_engine\",\n    \"version\": \"1.0.0\"\n  },\n  \"paths\": []\n}\nGeometry model JSON generated:\n{\n  \"models\": {\n    \"H_Freze20mm_Ic_SF\": {\n      \"paths\": {\n        \"rect_bottom_41\": {\n          \"end\": [\n            676.0,\n            756.0\n          ],\n          \"origin\": [\n            1096.0,\n            756.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_44\": {\n          \"end\": [\n            676.0,\n            136.0\n          ],\n          \"origin\": [\n            676.0,\n            756.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_43\": {\n          \"end\": [\n            1096.0,\n            756.0\n          ],\n          \"origin\": [\n            1096.0,\n            136.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_42\": {\n          \"end\": [\n            1096.0,\n            136.0\n          ],\n          \"origin\": [\n            676.0,\n            136.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"H_Freze5mm_Ic\": {\n      \"paths\": {\n        \"polyline_seg_10\": {\n          \"end\": [\n            290.0,\n            238.174\n          ],\n          \"origin\": [\n            460.348,\n            153.0\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_11\": {\n          \"end\": [\n            119.652,\n            153.0\n          ],\n          \"origin\": [\n            290.0,\n            238.174\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_12\": {\n          \"end\": [\n            274.348,\n            246.0\n          ],\n          \"origin\": [\n            97.0,\n            157.326\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_13\": {\n          \"end\": [\n            97.0,\n            334.674\n          ],\n          \"origin\": [\n            274.348,\n            246.0\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_14\": {\n          \"end\": [\n            97.0,\n            157.326\n          ],\n          \"origin\": [\n            97.0,\n            334.674\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_15\": {\n          \"end\": [\n            483.0,\n            157.326\n          ],\n          \"origin\": [\n            305.652,\n            246.0\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_16\": {\n          \"end\": [\n            483.0,\n            334.674\n          ],\n          \"origin\": [\n            483.0,\n            157.326\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_17\": {\n          \"end\": [\n            305.652,\n            246.0\n          ],\n          \"origin\": [\n            483.0,\n            334.674\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_18\": {\n          \"end\": [\n            474.348,\n            346.0\n          ],\n          \"origin\": [\n            290.0,\n            253.826\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_19\": {\n          \"end\": [\n            290.0,\n            438.174\n          ],\n          \"origin\": [\n            474.348,\n            346.0\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_20\": {\n          \"end\": [\n            105.652,\n            346.0\n          ],\n          \
 Draw commands received: []
 MakerJS JSON received: {
  "models": {
    "H_Freze20mm_Ic_SF": {
      "paths": {
        "rect_bottom_41": {
          "end": [
            676.0,
            756.0
          ],
          "origin": [
            1096.0,
 ...
 Processing makerjs JSON: {
  "models": {
    "H_Freze20mm_Ic_SF": {
      "paths": {
        "rect_bottom_41": {
          "end": [
            676.0,
            756.0
          ],
          "origin": [
            1096.0,
 ...
VisualizationPanel.vue:1084 Converted makerjs JSON to 79 draw commands
GraphicsCanvas.vue:513 GraphicsCanvas received draw commands: [
  {
    "command_type": "line",
    "x1": 1096,
    "y1": 756,
    "x2": 676,
    "y2": 756,
    "radius": 0,
    "color": "#DC143C",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze20mm_Ic_SF"
  },
  {
    "command_type": "line",
    "x1": 676,
    "y1": 756,
    "x2": 676,
    "y2": 136,
    "radius": 0,
    "color": "#DC143C",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze20mm_Ic_SF"
  },
  {
    "command_type": "line",
    "x1": 1096,
    "y1": 136,
    "x2": 1096,
    "y2": 756,
    "radius": 0,
    "color": "#DC143C",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze20mm_Ic_SF"
  },
  {
    "command_type": "line",
    "x1": 676,
    "y1": 136,
    "x2": 1096,
    "y2": 136,
    "radius": 0,
    "color": "#DC143C",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze20mm_Ic_SF"
  },
  {
    "command_type": "line",
    "x1": 460.348,
    "y1": 153,
    "x2": 290,
    "y2": 238.174,
    "radius": 0,
    "color": "#FF4500",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze5mm_Ic"
  },
  {
    "command_type": "line",
    "x1": 290,
    "y1": 238.174,
    "x2": 119.652,
    "y2": 153,
    "radius": 0,
    "color": "#FF4500",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze5mm_Ic"
  },
  {
    "command_type": "line",
    "x1": 97,
    "y1": 157.326,
    "x2": 274.348,
    "y2": 246,
    "radius": 0,
    "color": "#FF4500",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze5mm_Ic"
  },
  {
    "command_type": "line",
    "x1": 274.348,
    "y1": 246,
    "x2": 97,
    "y2": 334.674,
    "radius": 0,
    "color": "#FF4500",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze5mm_Ic"
  },
  {
    "command_type": "line",
    "x1": 97,
    "y1": 334.674,
    "x2": 97,
    "y2": 157.326,
    "radius": 0,
    "color": "#FF4500",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze5mm_Ic"
  },
  {
    "command_type": "line",
    "x1": 305.652,
    "y1": 246,
    "x2": 483,
    "y2": 157.326,
    "radius": 0,
    "color": "#FF4500",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze5mm_Ic"
  },
  {
    "command_type": "line",
    "x1": 483,
    "y1": 157.326,
    "x2": 483,
    "y2": 334.674,
    "radius": 0,
    "color": "#FF4500",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze5mm_Ic"
  },
  {
    "command_type": "line",
    "x1": 483,
    "y1": 334.674,
    "x2": 305.652,
    "y2": 246,
    "radius": 0,
    "color": "#FF4500",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze5mm_Ic"
  },
  {
    "command_type": "line",
    "x1": 290,
    "y1": 253.826,
    "x2": 474.348,
    "y2": 346,
    "radius": 0,
    "color": "#FF4500",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze5mm_Ic"
  },
  {
    "command_type": "line",
    "x1": 474.348,
    "y1": 346,
    "x2": 290,
    "y2": 438.174,
    "radius": 0,
    "color": "#FF4500",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze5mm_Ic"
  },
  {
    "command_type": "line",
    "x1": 290,
    "y1": 438.174,
    "x2": 105.652,
    "y2": 346,
    "radius": 0,
    "color": "#FF4500",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze5mm_Ic"
  },
  {
    "command_type": "line",
    "x1": 105.652,
    "y1": 346,
    "x2": 290,
    "y2": 253.826,
    "radius": 0,
    "color": "#FF4500",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze5mm_Ic"
  },
  {
    "command_type": "line",
    "x1": 305.652,
    "y1": 446,
    "x2": 483,
    "y2": 357.326,
    "radius": 0,
    "color": "#FF4500",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze5mm_Ic"
  },
  {
    "command_type": "line",
    "x1": 483,
    "y1": 357.326,
    "x2": 483,
    "y2": 534.674,
    "radius": 0,
    "color": "#FF4500",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze5mm_Ic"
  },
  {
    "command_type": "line",
    "x1": 483,
    "y1": 534.674,
    "x2": 305.652,
    "y2": 446,
    "radius": 0,
    "color": "#FF4500",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze5mm_Ic"
  },
  {
    "command_type": "line",
    "x1": 119.652,
    "y1": 739,
    "x2": 290,
    "y2": 653.826,
    "radius": 0,
    "color": "#FF4500",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze5mm_Ic"
  },
  {
    "command_type": "line",
    "x1": 290,
    "y1": 653.826,
    "x2": 460.348,
    "y2": 739,
    "radius": 0,
    "color": "#FF4500",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze5mm_Ic"
  },
  {
    "command_type": "line",
    "x1": 460.348,
    "y1": 739,
    "x2": 119.652,
    "y2": 739,
    "radius": 0,
    "color": "#FF4500",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze5mm_Ic"
  },
  {
    "command_type": "line",
    "x1": 97,
    "y1": 734.674,
    "x2": 97,
    "y2": 557.326,
    "radius": 0,
    "color": "#FF4500",
    "size": 1,
    "text": "",
    "layer_name": "H_Freze5mm_Ic"
  },
  {
    "command_type": "line",
    "x1": 97,
    "y1": 557.326,
    "x2": 274.348,
    "y2": 646,
    "radius": 0,
    "color": "#FF4500",
    "size": 1,
    "tex
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1200 x 660
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1200 x 660
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1200 x 660
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
OCJSCanvas.vue:116 🚀 WORKER-BASED PROCESSING STARTED
OCJSCanvas.vue:131 🔍 Parsed commands: Object
OCJSCanvas.vue:140 🔧 Using OCJS Service to extract door parameters
OCJSCanvas.vue:142 📏 Extracted door parameters: Object
OCJSCanvas.vue:152 Door parameters: Object
OCJSCanvas.vue:153 Door size in mm: W=500 H=700 T=18
OCJSCanvas.vue:170 ⚙️ Sending to worker: Object
ocjsService.ts:150 Creating door body with params: Object
ocjsWorker.ts:303 Worker received message: createDoorBody
OCJSCanvas.vue:582 OCJSCanvas mounted
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:107 No canvas ref, retrying in 100ms...
GraphicsCanvas.vue:113 Canvas ref still not available after retry
GraphicsCanvas.vue:119  Canvas ref permanently unavailable - component may not be mounted
(anonymous) @ GraphicsCanvas.vue:119
OCJSCanvas.vue:550 🧪 Testing OCJS Worker connectivity...
OCJSCanvas.vue:553 📞 Calling ocjsService.testWorker()
ocjsWorker.ts:52 OpenCascade.js initialized in worker
ocjsWorker.ts:140 Creating box with dimensions: 0.5 0.7 0.018
ocjsWorker.ts:331 Worker completed: createDoorBody
ocjsWorker.ts:303 Worker received message: createDoorBody
ocjsWorker.ts:140 Creating box with dimensions: 100 100 18
ocjsService.ts:154 Door body created successfully
ocjsService.ts:160 Processing 1 top face tools
ocjsService.ts:211  Error processing door with tools: DataCloneError: Failed to execute 'postMessage' on 'Worker': #<Object> could not be cloned.
    at ocjsService.ts:93:19
    at new Promise (<anonymous>)
    at OCJSService.sendMessage (ocjsService.ts:83:12)
    at OCJSService.createToolGeometry (ocjsService.ts:112:17)
    at OCJSService.processDoorWithTools (ocjsService.ts:163:45)
    at async processDrawCommands (OCJSCanvas.vue:173:21)
processDoorWithTools @ ocjsService.ts:211
OCJSCanvas.vue:210  OCJS Worker processing error: DataCloneError: Failed to execute 'postMessage' on 'Worker': #<Object> could not be cloned.
    at ocjsService.ts:93:19
    at new Promise (<anonymous>)
    at OCJSService.sendMessage (ocjsService.ts:83:12)
    at OCJSService.createToolGeometry (ocjsService.ts:112:17)
    at OCJSService.processDoorWithTools (ocjsService.ts:163:45)
    at async processDrawCommands (OCJSCanvas.vue:173:21)
processDrawCommands @ OCJSCanvas.vue:210
ocjsWorker.ts:331 Worker completed: createDoorBody
ocjsService.ts:135 OC.js Worker test successful
OCJSCanvas.vue:564 ✅ OCJS Worker is working correctly
localhost/:1  Uncaught (in promise) dialog.message not allowed. Permissions associated with this command: dialog:allow-message, dialog:default
