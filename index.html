<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Adek<PERSON>a Editor</title>
    <style>
      /* Loading screen styles */
      #app-loader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        color: #ffffff;
        transition: opacity 0.5s ease-out, visibility 0.5s ease-out;
      }

      #app-loader.hidden {
        opacity: 0;
        visibility: hidden;
      }

      .loader-logo {
        width: 80px;
        height: 80px;
        margin-bottom: 30px;
        background: linear-gradient(45deg, #007acc, #00a8ff);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        font-weight: bold;
        color: white;
        box-shadow: 0 8px 32px rgba(0, 122, 204, 0.3);
        animation: logoFloat 2s ease-in-out infinite alternate;
      }

      @keyframes logoFloat {
        0% { transform: translateY(0px); }
        100% { transform: translateY(-10px); }
      }

      .loader-title {
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 10px;
        background: linear-gradient(45deg, #007acc, #00a8ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .loader-subtitle {
        font-size: 16px;
        color: #b0b0b0;
        margin-bottom: 40px;
      }

      .loader-progress {
        width: 300px;
        height: 4px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
        overflow: hidden;
        margin-bottom: 20px;
      }

      .loader-progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #007acc, #00a8ff);
        border-radius: 2px;
        width: 0%;
        transition: width 0.3s ease;
        animation: progressGlow 2s ease-in-out infinite alternate;
      }

      @keyframes progressGlow {
        0% { box-shadow: 0 0 5px rgba(0, 122, 204, 0.5); }
        100% { box-shadow: 0 0 20px rgba(0, 122, 204, 0.8); }
      }

      .loader-status {
        font-size: 14px;
        color: #888;
        min-height: 20px;
        text-align: center;
      }

      .loader-dots {
        display: inline-block;
        animation: dots 1.5s infinite;
      }

      @keyframes dots {
        0%, 20% { content: ''; }
        40% { content: '.'; }
        60% { content: '..'; }
        80%, 100% { content: '...'; }
      }

      .loader-dots::after {
        content: '';
        animation: dots 1.5s infinite;
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="app-loader">
      <div class="loader-logo">L</div>
      <div class="loader-title">Adeko Lua Editor</div>
      <div class="loader-subtitle">Professional Lua Development Environment</div>
      <div class="loader-progress">
        <div class="loader-progress-bar" id="progress-bar"></div>
      </div>
      <div class="loader-status" id="loader-status">
        Initializing<span class="loader-dots"></span>
      </div>
    </div>

    <!-- Main App -->
    <div id="app"></div>

    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
