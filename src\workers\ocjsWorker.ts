// Import OpenCascade.js dynamically to avoid issues with WASM loading
// FIXED VERSION 2.0 - Cache Buster: 2025-01-04-10:30
import type { CNCTool, DrawCommand } from '../types'

// Worker message types
export interface OCJSWorkerMessage {
  id: string
  type: 'createDoorBody' | 'createToolGeometry' | 'performSweepOperation' | 'exportGLB'
  data: any
}

export interface OCJSWorkerResponse {
  id: string
  type: 'success' | 'error'
  data?: any
  error?: string
}

// Door body creation parameters
export interface DoorBodyParams {
  width: number
  height: number
  thickness: number
  cornerRadius?: number
}

// Tool geometry creation parameters
export interface ToolGeometryParams {
  tool: CNCTool
  commands: DrawCommand[]
  depth: number
  isBottomFace: boolean
}

// Sweep operation parameters
export interface SweepOperationParams {
  doorBodyShape: any // OC shape reference
  toolGeometries: any[] // Array of OC tool shapes
  operation: 'subtract' | 'union' | 'intersect'
}

let oc: any = null
let isInitialized = false

// Initialize OpenCascade.js
async function initializeOC() {
  if (!isInitialized) {
    try {
      // Dynamic import to handle WASM loading properly
      const { default: initOpenCascade } = await import('opencascade.js')
      oc = await initOpenCascade()
      isInitialized = true
      console.log('OpenCascade.js initialized in worker')
    } catch (error) {
      console.error('Failed to initialize OpenCascade.js:', error)
      throw error
    }
  }
  return oc
}

// Create door body from PANEL layer data
function createDoorBody(params: DoorBodyParams): any {
  const { width, height, thickness, cornerRadius = 0 } = params
  
  try {
    if (cornerRadius > 0) {
      // Create rounded rectangle door body
      const sketch = new oc.BRepBuilderAPI_MakeWire_1()
      
      // Create rounded rectangle profile
      const halfWidth = width / 2
      const halfHeight = height / 2
      const r = Math.min(cornerRadius, Math.min(halfWidth, halfHeight))
      
      // Create corner arcs and connecting lines
      const p1 = new oc.gp_Pnt_3(-halfWidth + r, -halfHeight, 0)
      const p2 = new oc.gp_Pnt_3(halfWidth - r, -halfHeight, 0)
      const p3 = new oc.gp_Pnt_3(halfWidth, -halfHeight + r, 0)
      const p4 = new oc.gp_Pnt_3(halfWidth, halfHeight - r, 0)
      const p5 = new oc.gp_Pnt_3(halfWidth - r, halfHeight, 0)
      const p6 = new oc.gp_Pnt_3(-halfWidth + r, halfHeight, 0)
      const p7 = new oc.gp_Pnt_3(-halfWidth, halfHeight - r, 0)
      const p8 = new oc.gp_Pnt_3(-halfWidth, -halfHeight + r, 0)
      
      // Create edges
      const edge1 = new oc.BRepBuilderAPI_MakeEdge_3(p1, p2).Edge()
      const edge2 = new oc.BRepBuilderAPI_MakeEdge_8(
        new oc.gp_Circ_3(
          new oc.gp_Ax2_3(new oc.gp_Pnt_3(halfWidth - r, -halfHeight + r, 0), new oc.gp_Dir_4(0, 0, 1)),
          r
        ),
        p2, p3
      ).Edge()
      const edge3 = new oc.BRepBuilderAPI_MakeEdge_3(p3, p4).Edge()
      const edge4 = new oc.BRepBuilderAPI_MakeEdge_8(
        new oc.gp_Circ_3(
          new oc.gp_Ax2_3(new oc.gp_Pnt_3(halfWidth - r, halfHeight - r, 0), new oc.gp_Dir_4(0, 0, 1)),
          r
        ),
        p4, p5
      ).Edge()
      const edge5 = new oc.BRepBuilderAPI_MakeEdge_3(p5, p6).Edge()
      const edge6 = new oc.BRepBuilderAPI_MakeEdge_8(
        new oc.gp_Circ_3(
          new oc.gp_Ax2_3(new oc.gp_Pnt_3(-halfWidth + r, halfHeight - r, 0), new oc.gp_Dir_4(0, 0, 1)),
          r
        ),
        p6, p7
      ).Edge()
      const edge7 = new oc.BRepBuilderAPI_MakeEdge_3(p7, p8).Edge()
      const edge8 = new oc.BRepBuilderAPI_MakeEdge_8(
        new oc.gp_Circ_3(
          new oc.gp_Ax2_3(new oc.gp_Pnt_3(-halfWidth + r, -halfHeight + r, 0), new oc.gp_Dir_4(0, 0, 1)),
          r
        ),
        p8, p1
      ).Edge()
      
      // Add edges to wire
      sketch.Add(edge1)
      sketch.Add(edge2)
      sketch.Add(edge3)
      sketch.Add(edge4)
      sketch.Add(edge5)
      sketch.Add(edge6)
      sketch.Add(edge7)
      sketch.Add(edge8)
      
      const wire = sketch.Wire()
      const face = new oc.BRepBuilderAPI_MakeFace_15(wire, false).Face()
      
      // Extrude to create 3D body
      const extrudeVec = new oc.gp_Vec_4(0, 0, thickness)
      const prism = new oc.BRepPrimAPI_MakePrism_1(face, extrudeVec, false, true)
      
      return prism.Shape()
    } else {
      // Create simple rectangular door body
      // BRepPrimAPI_MakeBox_2 expects only 3 parameters (dx, dy, dz) - FIXED
      console.log('Creating box with dimensions:', width, height, thickness)
      const box = new oc.BRepPrimAPI_MakeBox_2(width, height, thickness)

      // Move the box to be centered at origin
      const tf = new oc.gp_Trsf_1()
      tf.SetTranslation_1(new oc.gp_Vec_4(-width/2, -height/2, 0))
      const loc = new oc.TopLoc_Location_2(tf)

      const _doorShape = box.Shape().Moved(loc, false)

      // Return serializable data instead of OpenCascade objects
      return {
        success: true,
        shapeId: `door_${Date.now()}`,
        dimensions: { width, height, thickness }
        // Store the actual shape in a global cache for later use
      }
    }
  } catch (error) {
    console.error('Error creating door body:', error)
    throw error
  }
}

// Create tool geometry based on tool type and commands
function createToolGeometry(params: ToolGeometryParams): any {
  const { tool, commands, depth, isBottomFace } = params
  
  try {
    const toolGeometries: any[] = []
    
    commands.forEach(command => {
      let toolShape: any = null
      
      if (tool.shape === 'cylindrical') {
        // Create cylindrical tool
        const radius = tool.diameter / 2
        const cylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, depth)
        toolShape = cylinder.Shape()
      } else if (tool.shape === 'conical') {
        // Create conical tool (V-bit)
        const topRadius = tool.diameter / 2
        const bottomRadius = 0.1 // Small tip radius
        const cone = new oc.BRepPrimAPI_MakeCone_1(bottomRadius, topRadius, depth)
        toolShape = cone.Shape()
      } else if (tool.shape === 'ballnose') {
        // Create ballnose tool (hemisphere + cylinder)
        const radius = tool.diameter / 2
        const cylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, depth - radius)
        const sphere = new oc.BRepPrimAPI_MakeSphere_1(radius)
        
        // Position sphere at bottom of cylinder
        const tf = new oc.gp_Trsf_1()
        tf.SetTranslation_1(new oc.gp_Vec_4(0, 0, -radius))
        const loc = new oc.TopLoc_Location_2(tf)
        const movedSphere = sphere.Shape().Moved(loc, false)
        
        // Fuse cylinder and sphere
        const fuse = new oc.BRepAlgoAPI_Fuse_3(
          cylinder.Shape(),
          movedSphere,
          new oc.Message_ProgressRange_1()
        )
        fuse.Build(new oc.Message_ProgressRange_1())
        toolShape = fuse.Shape()
      }
      
      if (toolShape) {
        // Position tool based on command coordinates
        const tf = new oc.gp_Trsf_1()
        
        if (command.command_type === 'line') {
          const midX = (command.x1 + command.x2) / 2
          const midZ = (command.y1 + command.y2) / 2
          const posY = isBottomFace ? -depth : 0
          tf.SetTranslation_1(new oc.gp_Vec_4(midX, posY, midZ))
        } else if (command.command_type === 'circle') {
          const posY = isBottomFace ? -depth : 0
          // For circles, use x1,y1 as center coordinates
          tf.SetTranslation_1(new oc.gp_Vec_4(command.x1, posY, command.y1))
        }
        
        const loc = new oc.TopLoc_Location_2(tf)
        const positionedTool = toolShape.Moved(loc, false)
        toolGeometries.push(positionedTool)
      }
    })
    
    // Return serializable data instead of OpenCascade objects
    return {
      count: toolGeometries.length,
      success: true,
      // Don't return the actual geometries as they can't be serialized
      // Store them in a global cache for later use
      geometryIds: toolGeometries.map((_, index) => `tool_${Date.now()}_${index}`)
    }
  } catch (error) {
    console.error('Error creating tool geometry:', error)
    throw error
  }
}

// Perform sweep operation (boolean subtraction)
function performSweepOperation(params: SweepOperationParams): any {
  const { doorBodyShape, toolGeometries, operation } = params
  
  try {
    let resultShape = doorBodyShape
    
    toolGeometries.forEach(toolShape => {
      if (operation === 'subtract') {
        const cut = new oc.BRepAlgoAPI_Cut_3(
          resultShape,
          toolShape,
          new oc.Message_ProgressRange_1()
        )
        cut.Build(new oc.Message_ProgressRange_1())
        if (cut.IsDone()) {
          resultShape = cut.Shape()
        }
      } else if (operation === 'union') {
        const fuse = new oc.BRepAlgoAPI_Fuse_3(
          resultShape,
          toolShape,
          new oc.Message_ProgressRange_1()
        )
        fuse.Build(new oc.Message_ProgressRange_1())
        if (fuse.IsDone()) {
          resultShape = fuse.Shape()
        }
      }
    })
    
    // Return serializable data instead of OpenCascade objects
    return {
      success: true,
      shapeId: `result_${Date.now()}`,
      // Store the actual shape in a global cache for later use
      // Don't return the actual shape as it can't be serialized
    }
  } catch (error) {
    console.error('Error performing sweep operation:', error)
    throw error
  }
}

// Export shape to GLB format
function exportToGLB(shape: any): ArrayBuffer {
  try {
    // Create a document and add our shape
    const docHandle = new oc.Handle_TDocStd_Document_2(
      new oc.TDocStd_Document(new oc.TCollection_ExtendedString_1())
    )
    const shapeTool = oc.XCAFDoc_DocumentTool.ShapeTool(docHandle.get().Main()).get()
    shapeTool.SetShape(shapeTool.NewShape(), shape)

    // Mesh the shape
    new oc.BRepMesh_IncrementalMesh_2(shape, 0.1, false, 0.1, false)

    // Export GLB file
    const cafWriter = new oc.RWGltf_CafWriter(
      new oc.TCollection_AsciiString_2('./result.glb'),
      true
    )
    cafWriter.Perform_2(
      docHandle,
      new oc.TColStd_IndexedDataMapOfStringString_1(),
      new oc.Message_ProgressRange_1()
    )

    // Read the GLB file from virtual file system
    const glbFile = oc.FS.readFile('./result.glb', { encoding: 'binary' })
    return glbFile.buffer
  } catch (error) {
    console.error('Error exporting to GLB:', error)
    throw error
  }
}

// Worker message handler
self.onmessage = async (event: MessageEvent<OCJSWorkerMessage>) => {
  const { id, type, data } = event.data

  try {
    console.log(`Worker received message: ${type}`)
    await initializeOC()

    let result: any = null

    switch (type) {
      case 'createDoorBody':
        result = createDoorBody(data as DoorBodyParams)
        break
      case 'createToolGeometry':
        result = createToolGeometry(data as ToolGeometryParams)
        break
      case 'performSweepOperation':
        result = performSweepOperation(data as SweepOperationParams)
        break
      case 'exportGLB':
        result = exportToGLB(data)
        break
      default:
        throw new Error(`Unknown operation type: ${type}`)
    }

    const response: OCJSWorkerResponse = {
      id,
      type: 'success',
      data: result
    }

    console.log(`Worker completed: ${type}`)
    self.postMessage(response)
  } catch (error) {
    console.error(`Worker error for ${type}:`, error)
    const response: OCJSWorkerResponse = {
      id,
      type: 'error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }

    self.postMessage(response)
  }
}

// Handle worker errors
self.onerror = (error) => {
  console.error('Worker global error:', error)
}

self.onunhandledrejection = (event) => {
  console.error('Worker unhandled rejection:', event.reason)
}
